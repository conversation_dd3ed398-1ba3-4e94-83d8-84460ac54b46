<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-MPCBJDMQZV"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-MPCBJDMQZV');
    </script>

    <!-- Favicon links -->
    <link rel="icon" type="image/svg+xml" href="/MovieHub/icons/favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/MovieHub/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/MovieHub/icons/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/MovieHub/icons/apple-touch-icon.png">
    <link rel="manifest" href="/MovieHub/icons/site.webmanifest">
    <meta name="theme-color" content="#1E293B">

    <title>Movie Explorer</title>

    <script>
      // GitHub Pages SPA redirect handling
      // Store the current path and redirect to index
      const path = location.pathname + location.search + location.hash;
      sessionStorage.setItem('redirectPath', path);

      // Redirect to the base URL
      location.replace('/MovieHub/');
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/MovieHub/src/main.tsx"></script>
  </body>
</html>
