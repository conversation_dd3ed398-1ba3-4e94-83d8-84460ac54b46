
import './App.css'
import { BrowserRouter, Routes, Route, useNavigate } from 'react-router-dom'
import { useEffect } from 'react'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import SearchPage from './pages/SearchPage'
import MovieDetailsPage from './pages/MovieDetailsPage'
import ListsPage from './pages/ListsPage'

function AppContent() {
  const navigate = useNavigate();

  useEffect(() => {
    // Handle redirect from 404.html
    const redirectPath = sessionStorage.getItem('redirectPath');
    if (redirectPath) {
      sessionStorage.removeItem('redirectPath');
      // Remove the base path from the redirect path
      const cleanPath = redirectPath.replace('/MovieHub', '');
      if (cleanPath && cleanPath !== '/') {
        navigate(cleanPath, { replace: true });
      }
    }
  }, [navigate]);

  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route index element={<HomePage />} />
        <Route path="search" element={<SearchPage />} />
        <Route path="movie/:movieId" element={<MovieDetailsPage />} />
        <Route path="lists" element={<ListsPage />} />
      </Route>
    </Routes>
  );
}

function App() {
  return (
    // Change BrowserRouter to handle the base URL
    <BrowserRouter basename="/MovieHub">
      <AppContent />
    </BrowserRouter>
  )
}

export default App